<?php

namespace App\Http\Resources;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

class UserResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     * @mixin User
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'is_anonymous' => $this->is_anonymous,
            'is_first_login' => $this->is_first_login,
            'is_premium' => $this->is_premium,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            'points' => $this->points,
            'avatar' => $this->avatar,
            'show_renewal_notification' => $this->shouldShowQuotaRenewalNotification(),
        ];
    }

    /**
     * Add a token to the response.
     *
     * @param string $token_name
     * @param array $abilities
     * @return $this
     */
    public function withToken(string $token_name = 'anonymous-token', array $abilities = ['*'])
    {
        // Create a more descriptive token name that includes timestamp for better tracking
        $enhancedTokenName = $token_name . '-' . now()->format('Y-m-d-H-i-s');

        $this->additional([
            'meta' => [
                'token' => $this->createToken($enhancedTokenName, $abilities)->plainTextToken,
            ],
        ]);

        return $this;
    }
}
