<?php

namespace Tests\Feature\Auth;

use App\Actions\Auth\RevokeOtherSessionsAction;
use App\Actions\Auth\SocialLoginAction;
use App\Enums\UserType;
use App\Models\SocialProvider;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\Sanctum;
use Laravel\Socialite\Contracts\User as SocialiteUser;
use Laravel\Socialite\Facades\Socialite;
use Mockery;
use Tests\TestCase;

class SingleSessionEnforcementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock Socialite
        $this->mockSocialite();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    private function mockSocialite(): void
    {
        $socialiteUser = Mockery::mock(SocialiteUser::class);
        $socialiteUser->shouldReceive('getId')->andReturn('123456789');
        $socialiteUser->shouldReceive('getEmail')->andReturn('<EMAIL>');
        $socialiteUser->shouldReceive('getName')->andReturn('Test User');
        $socialiteUser->shouldReceive('getAvatar')->andReturn(null);

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturnSelf();
        
        Socialite::shouldReceive('userFromToken')
            ->with('valid-token')
            ->andReturn($socialiteUser);
    }

    /** @test */
    public function test_social_login_revokes_existing_sessions()
    {
        // Create a user with existing tokens
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_anonymous' => false,
            'user_type' => UserType::User->value,
        ]);

        // Create some existing tokens to simulate active sessions
        $token1 = $user->createToken('device-1-token');
        $token2 = $user->createToken('device-2-token');
        $token3 = $user->createToken('web-token');

        // Verify tokens exist
        $this->assertEquals(3, $user->tokens()->count());

        // Perform social login
        $response = $this->postJson('/api/users/social-login', [
            'provider' => 'google',
            'token' => 'valid-token',
            'device_id' => 'new-device-123',
            'device_type' => 'android',
        ]);

        // Assert successful login
        $response->assertStatus(201);
        
        // Refresh user to get updated token count
        $user->refresh();
        
        // Verify all previous tokens were revoked
        $this->assertEquals(0, $user->tokens()->count());
        
        // Verify response contains new token
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'email',
            ],
            'meta' => [
                'token'
            ]
        ]);
    }

    /** @test */
    public function test_revoke_other_sessions_action_works_correctly()
    {
        $user = User::factory()->create();
        
        // Create multiple tokens
        $user->createToken('token-1');
        $user->createToken('token-2');
        $user->createToken('token-3');
        
        $this->assertEquals(3, $user->tokens()->count());
        
        // Use the action to revoke sessions
        $action = new RevokeOtherSessionsAction();
        $revokedCount = $action->handle($user, 'device-123', 'google');
        
        // Verify all tokens were revoked
        $this->assertEquals(3, $revokedCount);
        $this->assertEquals(0, $user->tokens()->count());
    }

    /** @test */
    public function test_revoke_all_sessions_action_works_correctly()
    {
        $user = User::factory()->create();
        
        // Create multiple tokens
        $user->createToken('token-1');
        $user->createToken('token-2');
        
        $this->assertEquals(2, $user->tokens()->count());
        
        // Use the action to revoke all sessions
        $action = new RevokeOtherSessionsAction();
        $revokedCount = $action->revokeAllSessions($user);
        
        // Verify all tokens were revoked
        $this->assertEquals(2, $revokedCount);
        $this->assertEquals(0, $user->tokens()->count());
    }

    /** @test */
    public function test_social_login_with_existing_social_provider_revokes_sessions()
    {
        // Create user with existing social provider
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_anonymous' => false,
        ]);

        // Create existing social provider
        SocialProvider::create([
            'user_id' => $user->id,
            'provider' => 'google',
            'provider_id' => '123456789',
            'last_token' => 'old-token',
            'email' => '<EMAIL>',
        ]);

        // Create existing tokens
        $user->createToken('old-session-1');
        $user->createToken('old-session-2');
        
        $this->assertEquals(2, $user->tokens()->count());

        // Perform social login with same provider
        $response = $this->postJson('/api/users/social-login', [
            'provider' => 'google',
            'token' => 'valid-token',
            'device_id' => 'new-device-456',
            'device_type' => 'ios',
        ]);

        $response->assertStatus(201);
        
        // Verify old tokens were revoked
        $user->refresh();
        $this->assertEquals(0, $user->tokens()->count());
    }

    /** @test */
    public function test_multiple_devices_cannot_have_simultaneous_sessions()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_anonymous' => false,
        ]);

        // First device login
        $response1 = $this->postJson('/api/users/social-login', [
            'provider' => 'google',
            'token' => 'valid-token',
            'device_id' => 'device-1',
            'device_type' => 'android',
        ]);

        $response1->assertStatus(201);
        $token1 = $response1->json('meta.token');

        // Verify first device can access protected route
        $response = $this->withHeader('Authorization', 'Bearer ' . $token1)
            ->getJson('/api/user/profile');
        $response->assertStatus(200);

        // Second device login (should revoke first device's session)
        $response2 = $this->postJson('/api/users/social-login', [
            'provider' => 'google',
            'token' => 'valid-token',
            'device_id' => 'device-2',
            'device_type' => 'ios',
        ]);

        $response2->assertStatus(201);
        $token2 = $response2->json('meta.token');

        // Verify first device's token is now invalid
        $response = $this->withHeader('Authorization', 'Bearer ' . $token1)
            ->getJson('/api/user/profile');
        $response->assertStatus(401);

        // Verify second device's token is valid
        $response = $this->withHeader('Authorization', 'Bearer ' . $token2)
            ->getJson('/api/user/profile');
        $response->assertStatus(200);
    }

    /** @test */
    public function test_session_enforcement_logs_are_created()
    {
        Log::spy();

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_anonymous' => false,
        ]);

        // Create existing tokens
        $user->createToken('existing-token');

        // Perform social login
        $this->postJson('/api/users/social-login', [
            'provider' => 'google',
            'token' => 'valid-token',
            'device_id' => 'test-device',
            'device_type' => 'android',
        ]);

        // Verify logs were created
        Log::shouldHaveReceived('info')
            ->with('Social login session enforcement completed', Mockery::type('array'))
            ->once();
    }
}
