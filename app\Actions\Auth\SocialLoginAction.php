<?php

namespace App\Actions\Auth;

use App\Actions\Auth\RevokeOtherSessionsAction;
use App\Enums\UserType;
use App\Models\DeviceInfo;
use App\Models\SocialProvider;
use App\Models\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laravel\Socialite\Contracts\User as SocialiteUser;
use Laravel\Socialite\Facades\Socialite;
use Symfony\Component\HttpKernel\Exception\HttpException;

class SocialLoginAction
{
    /**
     * Handle the social login process
     *
     * @param array<string, mixed> $data
     * @return User
     * @throws HttpException
     */
    public function handle(array $data): User
    {
        $provider = $data['provider'] ?? 'unknown';
        $token = $data['token'] ?? null;
        $deviceId = $data['device_id'] ?? null;

        try {
            $socialUser = $this->getSocialUser($provider, $token);

            return DB::transaction(function () use ($socialUser, $data, $provider, $deviceId) {
                $user = $this->findOrCreateUser($socialUser, $data);

                // Revoke all other sessions for this user to enforce single session
                $revokeAction = new RevokeOtherSessionsAction();
                $revokedCount = $revokeAction->handle($user, $deviceId, $provider);

                Log::info('Social login session enforcement completed', [
                    'user_id' => $user->id,
                    'provider' => $provider,
                    'device_id' => $deviceId,
                    'revoked_sessions' => $revokedCount,
                ]);

                if ($deviceId) {
                    $this->updateDeviceInfo($user, $data);
                }

                if ($socialUser->getAvatar()) {
                    $this->addAvatarToUser($user, $socialUser->getAvatar());
                }

                return $user;
            });
        } catch (ModelNotFoundException $e) {
            Log::error('User not found during social login', [
                'provider' => $provider,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            abort(404, 'User not found');
        } catch (HttpException $e) {
            // Re-throw HTTP exceptions
            throw $e;
        } catch (\Exception $e) {
            Log::error('Social login error', [
                'provider' => $provider,
                'exception_class' => get_class($e),
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
            abort(500, 'Error authenticating with social provider: ' . $e->getMessage());
        }
    }

    /**
     * Get user from social provider
     *
     * @param string $provider
     * @param string|null $token
     * @return SocialiteUser
     * @throws HttpException
     */
    private function getSocialUser(string $provider, ?string $token): SocialiteUser
    {
        if (!$token) {
            Log::error('Missing token for social login', ['provider' => $provider]);
            abort(400, 'Token is required for social login');
        }

        try {
            $socialUser = Socialite::driver($provider)->userFromToken($token);

            if (!$socialUser) {
                Log::error('Failed to retrieve user from social provider', [
                    'provider' => $provider,
                ]);
                abort(401, 'Failed to authenticate with social provider');
            }

            Log::info('Retrieved user from social provider', [
                'provider' => $provider,
                'provider_id' => $socialUser->getId(),
                'email' => $socialUser->getEmail(),
            ]);

            return $socialUser;
        } catch (\Exception $e) {
            Log::error('Error retrieving user from social provider', [
                'provider' => $provider,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            abort(401, 'Failed to authenticate with social provider: ' . $e->getMessage());
        }
    }

    /**
     * Find or create user based on social login data
     *
     * @param SocialiteUser $socialUser
     * @param array<string, mixed> $data
     * @return User
     * @throws ModelNotFoundException
     */
    private function findOrCreateUser(SocialiteUser $socialUser, array $data): User
    {
        $provider = $data['provider'];
        $token = $data['token'];
        $userId = $data['user_id'] ?? null;

        if ($userId) {
            $user = User::findOrFail($userId);
            $this->createSocialProvider($socialUser, $provider, $token, $user->id);
            return $user;
        }

        $socialProvider = SocialProvider::where('provider_id', $socialUser->getId())
            ->where('provider', $provider)
            ->first();

        if ($socialProvider) {
            $socialProvider->update([
                'last_token' => $token,
                'email' => $this->getEmail($socialUser, $provider),
            ]);

            $user = $socialProvider->user;

            if (!$user) {
                Log::error('Social provider exists but user not found', [
                    'provider' => $provider,
                    'provider_id' => $socialUser->getId(),
                    'social_provider_id' => $socialProvider->id,
                ]);

                // Delete the orphaned social provider
                $socialProvider->delete();

                return $this->createNewUser($socialUser, $data);
            }

            return $user;
        }

        // Check if user exists with the same email
        $user = User::where('email', $socialUser->getEmail())->first();

        if ($user) {
            // User exists but not linked to this social provider
            $this->createSocialProvider($socialUser, $provider, $token, $user->id);
            return $user;
        }

        // Check if we have an anonymous user from the device
        $anonymousUser = $this->findAnonymousUserByDeviceId($data['device_id'] ?? null);

        if ($anonymousUser && $anonymousUser->is_anonymous) {
            // Convert anonymous user to registered user
            $anonymousUser->update([
                'name' => $socialUser->getName() ?? 'User' . Str::random(5),
                'email' => $socialUser->getEmail(),
                'is_anonymous' => false,
                'is_first_login' => false,
            ]);

            // Create social provider for the converted user
            $this->createSocialProvider($socialUser, $provider, $token, $anonymousUser->id);

            Log::info('Converted anonymous user to registered user', [
                'user_id' => $anonymousUser->id,
                'provider' => $provider,
            ]);

            return $anonymousUser;
        }

        // Create new user
        return $this->createNewUser($socialUser, $data);
    }

    /**
     * Create a new user from social data
     *
     * @param SocialiteUser $socialUser
     * @param array<string, mixed> $data
     * @return User
     */
    private function createNewUser(SocialiteUser $socialUser, array $data): User
    {
        $provider = $data['provider'];
        $token = $data['token'];

        // Create new user
        $user = User::create([
            'name' => $socialUser->getName() ?? 'User' . Str::random(5),
            'email' => $socialUser->getEmail(),
            'password' => Hash::make(Str::random(16)),
            'user_type' => UserType::User,
            'is_anonymous' => false,
            'is_first_login' => true,
        ]);

        // Create social provider for the new user
        $this->createSocialProvider($socialUser, $provider, $token, $user->id);

        Log::info('Created new user from social login', [
            'user_id' => $user->id,
            'provider' => $provider,
            'email' => $socialUser->getEmail(),
        ]);

        return $user;
    }

    /**
     * Find anonymous user by device ID
     *
     * @param string|null $deviceId
     * @return User|null
     */
    private function findAnonymousUserByDeviceId(?string $deviceId): ?User
    {
        if (!$deviceId) {
            return null;
        }

        $deviceInfo = DeviceInfo::where('device_id', $deviceId)->first();

        if (!$deviceInfo) {
            return null;
        }

        return $deviceInfo->user;
    }

    /**
     * Update device info for a user
     *
     * @param User $user
     * @param array<string, mixed> $data
     * @return DeviceInfo
     */
    private function updateDeviceInfo(User $user, array $data): DeviceInfo
    {
        $deviceData = array_filter([
            'device_type' => $data['device_type'] ?? null,
            'device_model' => $data['device_model'] ?? null,
            'device_brand' => $data['device_brand'] ?? null,
            'app_version' => $data['app_version'] ?? null,
            'app_version_name' => $data['app_version_name'] ?? null,
            'language' => $data['language'] ?? null,
            'ip_address' => $data['ip_address'] ?? null,
            'location' => $data['location'] ?? null,
            'os_version' => $data['os_version'] ?? null,
        ]);

        $deviceInfo = $user->deviceInfo()->updateOrCreate(
            ['device_id' => $data['device_id']],
            $deviceData
        );

        Log::info('Updated device info for user', [
            'user_id' => $user->id,
            'device_id' => $data['device_id'],
        ]);

        return $deviceInfo;
    }

    /**
     * Create a social provider for a user
     *
     * @param SocialiteUser $socialUser
     * @param string $provider
     * @param string $token
     * @param int $userId
     * @return SocialProvider
     */
    private function createSocialProvider(SocialiteUser $socialUser, string $provider, string $token, int $userId): SocialProvider
    {
        $socialProvider = SocialProvider::create([
            'provider_id' => $socialUser->getId(),
            'provider' => $provider,
            'email' => $this->getEmail($socialUser, $provider),
            'last_token' => $token,
            'user_id' => $userId,
        ]);

        Log::info('Created social provider for user', [
            'user_id' => $userId,
            'provider' => $provider,
            'provider_id' => $socialUser->getId(),
        ]);

        return $socialProvider;
    }

    /**
     * Get email from social user or generate one if not available
     *
     * @param SocialiteUser $socialUser
     * @param string $provider
     * @return string
     */
    private function getEmail(SocialiteUser $socialUser, string $provider): string
    {
        $email = $socialUser->getEmail();

        if (!$email) {
            $email = $socialUser->getId() . '@' . $provider . '.com';
            Log::info('Generated email for social user', [
                'provider' => $provider,
                'provider_id' => $socialUser->getId(),
                'generated_email' => $email,
            ]);
        }

        return $email;
    }

    /**
     * Add avatar to user from social provider
     *
     * @param User $user
     * @param string $avatarUrl
     * @return void
     */
    private function addAvatarToUser(User $user, string $avatarUrl): void
    {
        try {
            // Add avatar to user's media collection
            $user->addMediaFromUrl($avatarUrl)
                ->toMediaCollection('avatar');

            Log::info('Added avatar to user', [
                'user_id' => $user->id,
                'avatar_url' => $avatarUrl,
            ]);
        } catch (\Exception $e) {
            Log::error('Error adding avatar to user', [
                'user_id' => $user->id,
                'avatar_url' => $avatarUrl,
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);
        }
    }
}
