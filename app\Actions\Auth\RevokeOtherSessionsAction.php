<?php

namespace App\Actions\Auth;

use App\Models\User;
use Illuminate\Support\Facades\Log;

class RevokeOtherSessionsAction
{
    /**
     * Revoke all existing sessions for a user except the current device
     *
     * @param User $user
     * @param string|null $currentDeviceId
     * @param string $provider
     * @return int Number of tokens revoked
     */
    public function handle(User $user, ?string $currentDeviceId = null, string $provider = 'social'): int
    {
        // Get all existing tokens for the user
        $existingTokens = $user->tokens();
        
        // If we have a current device ID, we want to keep tokens that might be associated with this device
        // For now, we'll revoke all tokens since Sanct<PERSON> doesn't directly link tokens to device IDs
        // In a future enhancement, we could store device_id in token name or abilities
        
        $tokenCount = $existingTokens->count();
        
        if ($tokenCount > 0) {
            Log::info('Revoking existing sessions for social login', [
                'user_id' => $user->id,
                'provider' => $provider,
                'current_device_id' => $currentDeviceId,
                'tokens_to_revoke' => $tokenCount,
            ]);
            
            // Delete all existing tokens
            $existingTokens->delete();
            
            Log::info('Successfully revoked existing sessions', [
                'user_id' => $user->id,
                'provider' => $provider,
                'tokens_revoked' => $tokenCount,
            ]);
        }
        
        return $tokenCount;
    }
    
    /**
     * Revoke all sessions for a user (used for complete logout)
     *
     * @param User $user
     * @return int Number of tokens revoked
     */
    public function revokeAllSessions(User $user): int
    {
        $tokenCount = $user->tokens()->count();
        
        if ($tokenCount > 0) {
            Log::info('Revoking all sessions for user', [
                'user_id' => $user->id,
                'tokens_to_revoke' => $tokenCount,
            ]);
            
            $user->tokens()->delete();
            
            Log::info('Successfully revoked all sessions', [
                'user_id' => $user->id,
                'tokens_revoked' => $tokenCount,
            ]);
        }
        
        return $tokenCount;
    }
}
